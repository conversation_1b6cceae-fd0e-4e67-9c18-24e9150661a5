# 商品采集功能说明

## 功能概述

商品采集功能允许用户根据筛选条件从淘宝热销商品库中采集商品数据，支持实时预览和批量采集。

## 主要特性

### 1. 筛选条件配置
- **商品类目**: 支持28个主要类目，包括美容美妆、母婴用品、服饰等
- **商品标签**: 爆品、高佣、秒杀引流、稀缺、热销、超级好价、新品、高曝品
- **商品价格**: 支持价格区间筛选
- **佣金比例**: 支持佣金率区间筛选
- **365天销量**: 支持销量区间筛选
- **店铺类型**: 天猫店、淘宝店
- **店铺等级**: 5星级到心级的完整等级体系
- **商品渠道**: 直播严选、天猫超市、源头优选等8个渠道

### 2. 主播选择
- 从数据库中加载所有活跃主播
- 支持按主播名称排序
- 使用主播的Cookie进行API认证

### 3. 采集控制
- **请求间隔**: 可设置1-60秒的请求间隔，避免频繁请求
- **每页数量**: 支持30、50、100条记录的分页采集
- **实时控制**: 支持开始/停止采集操作
- **状态监控**: 实时显示采集状态、当前页数、已采集数量

### 4. 数据处理
采集的商品数据包含以下字段：
- `itemId`: 商品ID
- `itemName`: 商品标题
- `itemPrice`: 商品价格
- `commission`: 佣金金额
- `soldQuantity30`: 30天销量
- `soldQuantityLive7d`: 7天直播销量
- `soldQuantityLive1d`: 1天直播销量
- `soldQuantity365`: 365天销量
- `channelSlotText`: 渠道信息
- `shopName`: 店铺名称
- `safeShopId`: 店铺ID
- `tcpCommission`: TCP佣金
- `sales_rise_compare_last_week`: 周环比增长率
- `placedOrdersIn30`: 30天主播成交数量
- `featureTags`: 特色标签

### 5. 实时日志
- 采集过程实时日志显示
- 成功/失败状态区分
- 每个商品的详细信息展示
- 支持日志清空功能

### 6. 数据预览
- 表格形式展示采集的商品数据
- 包含商品ID、标题、价格、佣金、销量、店铺等关键信息
- 支持长文本截断显示

## 使用流程

### 1. 准备工作
1. 确保主播已配置有效的Cookie
2. 安装必要的依赖包：`npm install`
3. 启动服务：`npm start`

### 2. 访问页面
访问 `http://localhost:3000/product-collection` 进入商品采集页面

### 3. 配置筛选条件
1. **选择主播**: 点击主播按钮选择要使用的主播账号
2. **设置筛选条件**: 根据需要选择商品类目、标签、价格等筛选条件
3. **配置采集参数**: 设置请求间隔和每页数量

### 4. 开始采集
1. 点击"开始采集"按钮
2. 系统会根据设置的间隔自动采集数据
3. 实时查看日志和预览表格中的数据
4. 可随时点击"停止采集"结束采集

## API接口

### 1. 获取主播列表
```
GET /api/product-collection/anchors
Headers: X-API-Key: [API密钥]
```

### 2. 采集商品数据
```
POST /api/product-collection/collect
Headers: 
  Content-Type: application/json
  X-API-Key: [API密钥]
Body:
{
  "anchorId": "主播ID",
  "filters": {
    "cateIds": "类目ID列表",
    "featured": "特色标签列表",
    "priceSelect": "价格区间",
    "commissionRateSelect": "佣金率区间",
    "soldQuantity365Select": "销量区间",
    "shopType": "店铺类型",
    "shopLevel": "店铺等级",
    "icTags": "商品渠道"
  },
  "pageNum": 1,
  "pageSize": 30
}
```

## 技术实现

### 1. 签名生成
- 使用MD5算法生成请求签名
- 签名字符串格式：`h5Token&timestamp&appKey&dataString`
- 从Cookie中提取h5Token进行认证

### 2. 数据转换
- 销量数据支持"万"、"千"单位的数字转换
- 增长率数据的百分比解析
- 主播成交数量的格式化处理

### 3. 错误处理
- Cookie失效检测和提示
- 网络请求失败重试机制
- 数据解析错误处理
- 用户友好的错误提示

## 注意事项

1. **Cookie有效性**: 确保主播Cookie有效，过期需要重新获取
2. **请求频率**: 建议设置合理的请求间隔，避免被限流
3. **数据量控制**: 大量数据采集时注意内存使用
4. **网络稳定性**: 确保网络连接稳定，避免采集中断

## 故障排除

### 1. Cookie无效
- 错误信息：`Cookie无效或需要重新登录`
- 解决方案：更新主播的Cookie信息

### 2. 请求失败
- 错误信息：`HTTP错误: 401/403`
- 解决方案：检查API密钥和主播权限

### 3. 数据解析失败
- 错误信息：`JSON解析失败`
- 解决方案：检查网络连接和API响应格式

### 4. 签名错误
- 错误信息：签名验证失败
- 解决方案：检查h5Token提取和签名生成逻辑

## 更新日志

### v1.0.0 (2025-01-31)
- 初始版本发布
- 支持基础的商品采集功能
- 实现筛选条件配置
- 添加实时日志和数据预览
- 支持多主播切换
