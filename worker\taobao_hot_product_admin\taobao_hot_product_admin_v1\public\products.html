<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>淘宝产品数据管理</title>

    <script src="https://registry.npmmirror.com/tailwindcss-cdn/3.4.10/files/tailwindcss.js"></script>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/xlsx.full.min.js" defer></script>
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/jquery-3.5.0.min.js"></script>
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/layer.3.5.1/layer.js"></script>
    <style>
        .table-container {
            overflow-x: auto;
        }

        @media (max-width: 640px) {
            .pagination-desktop {
                display: none;
            }

            .pagination-mobile {
                display: flex;
            }
        }

        @media (min-width: 641px) {
            .pagination-desktop {
                display: flex;
            }

            .pagination-mobile {
                display: none;
            }
        }

        .stat-card {
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .sort-icon {
            display: inline-block;
            width: 8px;
            height: 8px;
            margin-left: 5px;
        }

        .sort-asc::after {
            content: '▲';
            font-size: 10px;
        }

        .sort-desc::after {
            content: '▼';
            font-size: 10px;
        }

        th.sortable {
            cursor: pointer;
        }

        th.sortable:hover {
            background-color: #f3f4f6;
        }

        .content-width {
            width: 80%;
        }

        .date-range-btn {
            transition: all 0.2s ease;
        }

        /* 筛选按钮样式 */
        .product-source-btn.selected,
        .alliance-channel-btn.selected,
        .tag-btn.selected,
        .product-type-btn.selected {
            background-color: #f97316;
            border-color: #f97316;
            color: white;
        }

        .product-source-btn.selected:hover,
        .alliance-channel-btn.selected:hover,
        .tag-btn.selected:hover,
        .product-type-btn.selected:hover {
            background-color: #ea580c;
            border-color: #ea580c;
        }

        /* 全部按钮选中状态 */
        .product-source-all-btn.selected,
        .alliance-channel-all-btn.selected,
        .tag-all-btn.selected,
        .product-type-all-btn.selected {
            background-color: #2563eb;
            border-color: #2563eb;
            color: white;
        }

        .product-source-all-btn.selected:hover,
        .alliance-channel-all-btn.selected:hover,
        .tag-all-btn.selected:hover,
        .product-type-all-btn.selected:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
        }

        .date-range-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .date-range-btn.active {
            background-color: #fed7aa !important;
            color: #c2410c !important;
            border-color: #fb923c !important;
        }



        .price-range-inputs {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .price-range-inputs input {
            flex: 1;
            min-width: 0;
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-orange-600 shadow">
        <div class="content-width mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <span class="text-xl font-bold text-white"> 淘宝产品数据管理</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="products.html" class="bg-orange-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-box mr-1"></i>产品管理
                    </a>
                    <a href="selection-rules.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-filter mr-1"></i>选品规则
                    </a>
                    <a href="rule-management.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-cogs mr-1"></i>规则管理
                    </a>
                    <a href="anchors.html"
                        class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-microphone mr-1"></i>主播管理
                    </a>
                    <a href="product-collection-new.html"
                        class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-shopping-cart mr-1"></i>商品采集
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="content-width mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 筛选表单 -->
        <div class="bg-white shadow rounded-lg mb-6 p-4">
            <form id="filterForm" class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6">
                <!-- 主播筛选 -->
                <div class="sm:col-span-2">
                    <label for="anchorFilter" class="block text-sm font-medium text-gray-700">主播</label>
                    <div class="mt-1">
                        <select id="anchorFilter" name="anchor"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                            <option value="">全部主播</option>
                        </select>
                    </div>
                </div>

                <!-- 价格区间 -->
                <div class="sm:col-span-2">
                    <label class="block text-sm font-medium text-gray-700">价格区间</label>
                    <div class="mt-1 price-range-inputs">
                        <input type="number" id="minPrice" name="minPrice" placeholder="最低价"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                        <span class="text-gray-500">-</span>
                        <input type="number" id="maxPrice" name="maxPrice" placeholder="小于"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 批次号 -->
                <div class="sm:col-span-2">
                    <label for="batchNumberFilter" class="block text-sm font-medium text-gray-700">批次号</label>
                    <div class="mt-1">
                        <input type="text" id="batchNumberFilter" name="batchNumber" placeholder="输入批次号"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 佣金筛选 -->
                <div class="sm:col-span-2">
                    <label for="minCommission" class="block text-sm font-medium text-gray-700">佣金大于</label>
                    <div class="mt-1">
                        <input type="number" id="minCommission" name="minCommission" placeholder="输入金额" step="0.01"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 365天销量区间 -->
                <div class="sm:col-span-4">
                    <label class="block text-sm font-medium text-gray-700">365天销量区间</label>
                    <div class="mt-1 price-range-inputs">
                        <input type="number" id="minSales365" name="minSales365" placeholder="最小值"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                        <span class="text-gray-500">-</span>
                        <input type="number" id="maxSales365" name="maxSales365" placeholder="最大值"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 7天增长率筛选 -->
                <div class="sm:col-span-2">
                    <label for="minGrowthRate7Days" class="block text-sm font-medium text-gray-700">7天增长率大于(%)</label>
                    <div class="mt-1">
                        <input type="number" id="minGrowthRate7Days" name="minGrowthRate7Days" placeholder="输入百分比" step="0.1"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 产品来源多选 -->
                <div class="sm:col-span-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">产品来源</label>
                    <div class="flex flex-wrap gap-2">
                        <button type="button" class="product-source-all-btn px-3 py-1 text-sm border border-blue-300 rounded-md bg-blue-50 text-blue-700 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors font-medium">
                            全部
                        </button>
                        <button type="button" data-value="热浪联盟" class="product-source-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            热浪联盟
                        </button>
                        <button type="button" data-value="淘宝搜索" class="product-source-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            淘宝搜索
                        </button>
                        <button type="button" data-value="淘宝首页" class="product-source-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            淘宝首页
                        </button>
                        <button type="button" data-value="淘宝秒杀" class="product-source-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            淘宝秒杀
                        </button>
                        <button type="button" data-value="淘宝直播" class="product-source-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            淘宝直播
                        </button>
                        <button type="button" data-value="淘宝百亿补贴" class="product-source-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            淘宝百亿补贴
                        </button>
                        <button type="button" data-value="点击" class="product-source-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            点击
                        </button>
                        <button type="button" data-value="订单" class="product-source-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            订单
                        </button>
                    </div>
                </div>

                <!-- 联盟商品渠道多选 -->
                <div class="sm:col-span-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">联盟商品渠道</label>
                    <div class="flex flex-wrap gap-2">
                        <button type="button" class="alliance-channel-all-btn px-3 py-1 text-sm border border-blue-300 rounded-md bg-blue-50 text-blue-700 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors font-medium">
                            全部
                        </button>
                        <button type="button" data-value="直播严选" class="alliance-channel-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            直播严选
                        </button>
                        <button type="button" data-value="天猫超市" class="alliance-channel-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            天猫超市
                        </button>
                        <button type="button" data-value="源头优选" class="alliance-channel-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            源头优选
                        </button>
                        <button type="button" data-value="淘工厂" class="alliance-channel-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            淘工厂
                        </button>
                        <button type="button" data-value="天天热卖" class="alliance-channel-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            天天热卖
                        </button>
                        <button type="button" data-value="天猫U先" class="alliance-channel-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            天猫U先
                        </button>
                        <button type="button" data-value="淘宝秒杀" class="alliance-channel-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            淘宝秒杀
                        </button>
                        <button type="button" data-value="淘宝买菜" class="alliance-channel-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            淘宝买菜
                        </button>
                    </div>
                </div>

                <!-- 标签多选 -->
                <div class="sm:col-span-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">标签</label>
                    <div class="flex flex-wrap gap-2">
                        <button type="button" class="tag-all-btn px-3 py-1 text-sm border border-blue-300 rounded-md bg-blue-50 text-blue-700 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors font-medium">
                            全部
                        </button>
                        <button type="button" data-value="1" class="tag-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            大类目
                        </button>
                        <button type="button" data-value="2" class="tag-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            流量品
                        </button>
                        <button type="button" data-value="3" class="tag-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            店铺品
                        </button>
                        <button type="button" data-value="4" class="tag-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            出单同类品
                        </button>
                    </div>
                </div>

                <!-- 产品类型多选 -->
                <div class="sm:col-span-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">产品类型</label>
                    <div class="flex flex-wrap gap-2">
                        <button type="button" class="product-type-all-btn px-3 py-1 text-sm border border-blue-300 rounded-md bg-blue-50 text-blue-700 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors font-medium">
                            全部
                        </button>
                        <button type="button" data-value="通用" class="product-type-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            通用
                        </button>
                        <button type="button" data-value="严选" class="product-type-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            严选
                        </button>
                    </div>
                </div>

                <!-- 时间选择区间 -->
                <div class="sm:col-span-2">
                    <label for="startDate" class="block text-sm font-medium text-gray-700">开始日期</label>
                    <div class="mt-1">
                        <input type="date" name="startDate" id="startDate"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>
                <div class="sm:col-span-2">
                    <label for="endDate" class="block text-sm font-medium text-gray-700">结束日期</label>
                    <div class="mt-1">
                        <input type="date" name="endDate" id="endDate"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 快捷时间筛选 -->
                <div class="sm:col-span-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">快捷时间筛选</label>
                    <div class="flex flex-wrap gap-2">
                        <button type="button" id="todayBtn"
                            class="date-range-btn inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-calendar-day mr-1"></i> 今日
                        </button>
                        <button type="button" id="yesterdayBtn"
                            class="date-range-btn inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-calendar-minus mr-1"></i> 昨日
                        </button>
                        <button type="button" id="last7DaysBtn"
                            class="date-range-btn inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-calendar-week mr-1"></i> 最近7天
                        </button>
                        <button type="button" id="thisMonthBtn"
                            class="date-range-btn inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-calendar-alt mr-1"></i> 本月
                        </button>
                        <button type="button" id="lastMonthBtn"
                            class="date-range-btn inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-calendar-check mr-1"></i> 上月
                        </button>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="sm:col-span-6 flex justify-between items-center">
                    <div class="flex items-center">
                        <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-filter mr-2"></i> 查询
                        </button>
                        <button type="button" id="resetBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-redo mr-2"></i> 重置
                        </button>
                        <button type="button" id="exportBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <i class="fas fa-file-export mr-2"></i> 导出Excel
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-blue-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-box text-blue-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">总产品数</p>
                        <p class="text-2xl font-semibold text-gray-700" id="totalProducts">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-green-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-money-bill-wave text-green-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">平均价格</p>
                        <p class="text-2xl font-semibold text-gray-700" id="avgPrice">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-yellow-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-coins text-yellow-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">平均佣金</p>
                        <p class="text-2xl font-semibold text-gray-700" id="avgCommission">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-purple-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-chart-line text-purple-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">总销量(30天)</p>
                        <p class="text-2xl font-semibold text-gray-700" id="totalSales30">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-indigo-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-microphone text-indigo-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">主播数量</p>
                        <p class="text-2xl font-semibold text-gray-700" id="totalAnchors">--</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="bg-white shadow overflow-hidden rounded-lg">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">产品数据列表</h3>
                <div class="flex items-center">
                    <div class="text-sm text-gray-500 mr-4">共 <span id="totalCount">0</span> 条记录</div>
                </div>
            </div>
            <div class="border-t border-gray-200 table-container">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onclick="toggleSort('product_id')">
                                产品ID <span id="sort-product_id" class="sort-icon"></span>
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                产品标题
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onclick="toggleSort('product_price')">
                                价格 <span id="sort-product_price" class="sort-icon"></span>
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onclick="toggleSort('commission_rate')">
                                佣金率 <span id="sort-commission_rate" class="sort-icon"></span>
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onclick="toggleSort('commission_amount')">
                                佣金金额 <span id="sort-commission_amount" class="sort-icon"></span>
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                产品来源
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                联盟渠道
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onclick="toggleSort('sales_30_days')">
                                30天销量 <span id="sort-sales_30_days" class="sort-icon"></span>
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onclick="toggleSort('sales_7_days_growth_rate')">
                                7天销量增长率 <span id="sort-sales_7_days_growth_rate" class="sort-icon"></span>
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onclick="toggleSort('tag')">
                                标签 <span id="sort-tag" class="sort-icon"></span>
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onclick="toggleSort('product_type')">
                                产品类型 <span id="sort-product_type" class="sort-icon"></span>
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onclick="toggleSort('date')">
                                日期 <span id="sort-date" class="sort-icon"></span>
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                主播
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                批次号
                            </th>
                            <th scope="col"
                                class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onclick="toggleSort('created_at')">
                                创建时间 <span id="sort-created_at" class="sort-icon"></span>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="productsTable" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="15" class="px-6 py-4 text-center text-sm text-gray-500">加载中...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <!-- 移动端分页 -->
                <div class="pagination-mobile flex-1 flex justify-between">
                    <button id="prevPageMobile"
                        class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </button>
                    <div class="text-sm text-gray-700 flex items-center">
                        第 <span id="currentPageMobile" class="mx-1">1</span> / <span id="totalPagesMobile"
                            class="mx-1">1</span> 页
                    </div>
                    <button id="nextPageMobile"
                        class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </button>
                </div>
                <!-- 桌面端分页 -->
                <div class="pagination-desktop flex-1 sm:flex sm:items-center sm:justify-between ml-4">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span id="startItem" class="font-medium">1</span> 到第
                            <span id="endItem" class="font-medium">10</span> 条，共
                            <span id="totalItems" class="font-medium">0</span> 条
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination"
                            id="pagination">
                            <!-- 分页按钮将通过JavaScript填充 -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 访问密码输入弹窗 -->
    <div id="apiKeyModal" class="fixed z-10 inset-0 overflow-y-auto hidden">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div
                class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                请输入访问密码
                            </h3>
                            <div class="mt-4">
                                <form id="apiKeyForm">
                                    <div class="mb-4">
                                        <label for="apiKey"
                                            class="block text-sm font-medium text-gray-700">访问密码</label>
                                        <input type="password" name="apiKey" id="apiKey"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                                            required>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button id="submitApiKeyBtn" type="button"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-orange-600 text-base font-medium text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:ml-3 sm:w-auto sm:text-sm">
                        确认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 当前页码和每页显示数量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 0;
        let filters = {};
        let currentSortField = 'created_at';
        let currentSortOrder = 'DESC';
        let searchTimeout = null; // 防抖定时器

        // 多选框选中的值
        let selectedProductSources = [];
        let selectedAllianceChannels = [];
        let selectedTags = [];
        let selectedProductTypes = [];

        // 页面加载完成后获取数据
        $(document).ready(function () {
            if (typeof layer === 'undefined') {
                console.error('layer.js 未正确加载');
                return;
            }

            initEventListeners();

            // 检查是否有访问密码，如果没有则显示输入弹窗
            const apiKey = getCookie('api_key');
            if (!apiKey) {
                showApiKeyModal();
                return;
            }

            // 初始化多选框状态
            updateSelectedSources();
            updateSelectedChannels();

            // 并行加载数据以提高性能
            Promise.all([
                loadAnchorNames(),
                loadProductsAndStats()
            ]).catch(error => {
                console.error('初始化数据加载失败:', error);
            });
        });

        // 初始化事件监听器
        function initEventListeners() {
            // 筛选表单提交
            $('#filterForm').on('submit', function (e) {
                e.preventDefault();
                currentPage = 1;
                loadProducts();
            });

            // 重置按钮
            $('#resetBtn').on('click', function () {
                $('#filterForm')[0].reset();
                // 重置多选按钮
                $('.product-source-btn').removeClass('selected');
                $('.alliance-channel-btn').removeClass('selected');
                $('.tag-btn').removeClass('selected');
                $('.product-type-btn').removeClass('selected');
                // 重置全部按钮
                $('.product-source-all-btn').removeClass('selected');
                $('.alliance-channel-all-btn').removeClass('selected');
                $('.tag-all-btn').removeClass('selected');
                $('.product-type-all-btn').removeClass('selected');
                selectedProductSources = [];
                selectedAllianceChannels = [];
                selectedTags = [];
                selectedProductTypes = [];
                // 重置快捷时间按钮状态
                updateDateRangeButtonState('');
                currentPage = 1;
                loadProducts();
            });

            // 导出按钮
            $('#exportBtn').on('click', function () {
                exportToExcel();
            });

            // 移动端分页按钮
            $('#prevPageMobile').on('click', function () {
                if (currentPage > 1) {
                    currentPage--;
                    loadProducts();
                }
            });

            $('#nextPageMobile').on('click', function () {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadProducts();
                }
            });

            // 访问密码提交按钮
            $('#submitApiKeyBtn').on('click', function () {
                submitApiKey();
            });

            // 访问密码表单回车提交
            $('#apiKeyForm').on('submit', function (e) {
                e.preventDefault();
                submitApiKey();
            });

            // 快捷时间筛选按钮
            $('#todayBtn').on('click', function () {
                setDateRange('today');
            });

            $('#yesterdayBtn').on('click', function () {
                setDateRange('yesterday');
            });

            $('#last7DaysBtn').on('click', function () {
                setDateRange('last7Days');
            });

            $('#thisMonthBtn').on('click', function () {
                setDateRange('thisMonth');
            });

            $('#lastMonthBtn').on('click', function () {
                setDateRange('lastMonth');
            });

            // 筛选条件自动触发查询
            $('#anchorFilter').on('change', function () {
                debouncedSearch();
            });

            $('#minPrice').on('input', function () {
                debouncedSearch();
            });

            $('#maxPrice').on('input', function () {
                debouncedSearch();
            });

            $('#batchNumberFilter').on('input', function () {
                debouncedSearch();
            });

            $('#minCommission').on('input', function () {
                debouncedSearch();
            });

            $('#minSales365').on('input', function () {
                debouncedSearch();
            });

            $('#maxSales365').on('input', function () {
                debouncedSearch();
            });

            $('#minGrowthRate7Days').on('input', function () {
                debouncedSearch();
            });

            $('#startDate').on('change', function () {
                debouncedSearch();
            });

            $('#endDate').on('change', function () {
                debouncedSearch();
            });

            // 产品来源按钮事件
            $('.product-source-btn').on('click', function () {
                $(this).toggleClass('selected');
                updateSelectedSources();
                updateAllButtonState('product-source');
                debouncedSearch();
            });

            // 联盟渠道按钮事件
            $('.alliance-channel-btn').on('click', function () {
                $(this).toggleClass('selected');
                updateSelectedChannels();
                updateAllButtonState('alliance-channel');
                debouncedSearch();
            });

            // 标签按钮事件
            $('.tag-btn').on('click', function () {
                $(this).toggleClass('selected');
                updateSelectedTags();
                updateAllButtonState('tag');
                debouncedSearch();
            });

            // 产品类型按钮事件
            $('.product-type-btn').on('click', function () {
                $(this).toggleClass('selected');
                updateSelectedProductTypes();
                updateAllButtonState('product-type');
                debouncedSearch();
            });

            // 产品来源全部按钮事件
            $('.product-source-all-btn').on('click', function () {
                toggleAllSelection('product-source');
            });

            // 联盟渠道全部按钮事件
            $('.alliance-channel-all-btn').on('click', function () {
                toggleAllSelection('alliance-channel');
            });

            // 标签全部按钮事件
            $('.tag-all-btn').on('click', function () {
                toggleAllSelection('tag');
            });

            // 产品类型全部按钮事件
            $('.product-type-all-btn').on('click', function () {
                toggleAllSelection('product-type');
            });
        }

        // 更新选中的产品来源
        function updateSelectedSources() {
            selectedProductSources = [];
            $('.product-source-btn.selected').each(function() {
                selectedProductSources.push($(this).data('value'));
            });
        }

        // 更新选中的联盟渠道
        function updateSelectedChannels() {
            selectedAllianceChannels = [];
            $('.alliance-channel-btn.selected').each(function() {
                selectedAllianceChannels.push($(this).data('value'));
            });
        }

        // 更新选中的标签
        function updateSelectedTags() {
            selectedTags = [];
            $('.tag-btn.selected').each(function() {
                selectedTags.push($(this).data('value'));
            });
        }

        // 更新选中的产品类型
        function updateSelectedProductTypes() {
            selectedProductTypes = [];
            $('.product-type-btn.selected').each(function() {
                selectedProductTypes.push($(this).data('value'));
            });
        }

        // 切换全选状态
        function toggleAllSelection(type) {
            const allBtn = $(`.${type}-all-btn`);
            const itemBtns = $(`.${type}-btn`);

            if (allBtn.hasClass('selected')) {
                // 当前是全选状态，取消全选
                allBtn.removeClass('selected');
                itemBtns.removeClass('selected');
            } else {
                // 当前不是全选状态，执行全选
                allBtn.addClass('selected');
                itemBtns.addClass('selected');
            }

            // 更新对应的选中数组
            if (type === 'product-source') {
                updateSelectedSources();
            } else if (type === 'alliance-channel') {
                updateSelectedChannels();
            } else if (type === 'tag') {
                updateSelectedTags();
            } else if (type === 'product-type') {
                updateSelectedProductTypes();
            }

            // 触发搜索
            debouncedSearch();
        }

        // 更新全部按钮状态
        function updateAllButtonState(type) {
            const allBtn = $(`.${type}-all-btn`);
            const itemBtns = $(`.${type}-btn`);
            const selectedItemBtns = $(`.${type}-btn.selected`);

            // 如果所有单项都被选中，则全部按钮也应该被选中
            if (selectedItemBtns.length === itemBtns.length && itemBtns.length > 0) {
                allBtn.addClass('selected');
            } else {
                allBtn.removeClass('selected');
            }
        }

        // 防抖查询函数
        function debouncedSearch() {
            // 清除之前的定时器
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // 设置新的定时器，300ms后执行查询
            searchTimeout = setTimeout(function () {
                currentPage = 1;
                loadProducts();
            }, 300);
        }

        // 设置日期范围
        function setDateRange(type) {
            const today = new Date();
            const $startDateInput = $('#startDate');
            const $endDateInput = $('#endDate');

            let startDate, endDate;

            switch (type) {
                case 'today':
                    startDate = new Date(today);
                    endDate = new Date(today);
                    break;

                case 'yesterday':
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() - 1);
                    endDate = new Date(startDate);
                    break;

                case 'last7Days':
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() - 6); // 包含今天，所以是-6
                    endDate = new Date(today);
                    break;

                case 'thisMonth':
                    startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    endDate = new Date(today);
                    break;

                case 'lastMonth':
                    // 上个月的第一天
                    startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    // 上个月的最后一天
                    endDate = new Date(today.getFullYear(), today.getMonth(), 0);
                    break;

                default:
                    return;
            }

            // 格式化日期为YYYY-MM-DD格式
            const formatDate = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            };

            // 设置日期输入框的值
            $startDateInput.val(formatDate(startDate));
            $endDateInput.val(formatDate(endDate));

            // 更新按钮状态
            updateDateRangeButtonState(type);

            // 自动触发查询
            currentPage = 1;
            loadProducts();
        }

        // 更新日期范围按钮状态
        function updateDateRangeButtonState(activeType) {
            const buttons = ['todayBtn', 'yesterdayBtn', 'last7DaysBtn', 'thisMonthBtn', 'lastMonthBtn'];

            // 移除所有按钮的active状态
            buttons.forEach(btnId => {
                $('#' + btnId).removeClass('active');
            });

            // 高亮当前选中的按钮
            if (activeType) {
                $('#' + activeType + 'Btn').addClass('active');
            }
        }

        // 加载主播名称列表
        async function loadAnchorNames() {
            try {
                const response = await fetch("/api/anchors?mode=full", {
                    headers: addApiKeyHeader()
                });

                if (response.status === 401) {
                    const data = await response.json();
                    if (data.error === "invalid_api_key") {
                        showApiKeyModal();
                        return;
                    }
                }

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '获取主播列表失败');
                }

                const $selectElement = $('#anchorFilter');
                $selectElement.html('<option value="">全部主播</option>');

                if (data.anchors && data.anchors.length > 0) {
                    data.anchors.forEach(anchor => {
                        if (anchor.anchor_name) {
                            $selectElement.append($('<option>', {
                                value: anchor.anchor_name,
                                text: `${anchor.anchor_name}`,
                                'data-anchor-id': anchor.anchor_id
                            }));
                        }
                    });
                } else {
                    $selectElement.append($('<option>', {
                        value: '',
                        text: '暂无主播数据',
                        disabled: true
                    }));
                }
            } catch (error) {
                console.error('Error loading anchor names:', error);
                showMessage('加载主播列表失败: ' + error.message, false);
            }
        }

        // 加载产品数据和统计数据
        async function loadProductsAndStats() {
            try {
                // 显示加载状态
                showLoadingState();

                const formData = getFilterParams();
                const params = new URLSearchParams(formData);

                // 添加分页和排序参数
                params.append('page', currentPage);
                params.append('limit', pageSize);
                params.append('sortField', currentSortField);
                params.append('sortOrder', currentSortOrder);

                // 并行请求产品数据和统计数据
                const [productsResponse, statsResponse] = await Promise.all([
                    fetch(`/api/products?${params.toString()}`, {
                        headers: addApiKeyHeader()
                    }),
                    fetch(`/api/products/stats?${params.toString()}`, {
                        headers: addApiKeyHeader()
                    })
                ]);

                // 处理访问密码验证
                if (productsResponse.status === 401 || statsResponse.status === 401) {
                    const data = await productsResponse.json();
                    if (data.error === "invalid_api_key") {
                        showApiKeyModal();
                        return;
                    }
                }

                const [productsData, statsData] = await Promise.all([
                    productsResponse.json(),
                    statsResponse.json()
                ]);

                if (!productsResponse.ok) {
                    throw new Error(productsData.error || '获取产品数据失败');
                }

                if (!statsResponse.ok) {
                    console.warn('获取统计数据失败:', statsData.error);
                }

                // 更新产品列表
                updateProductsTable(productsData.products || []);
                updatePagination(productsData.pagination || {});

                // 更新统计数据
                if (statsResponse.ok) {
                    updateStatsCards(statsData);
                }

            } catch (error) {
                console.error('Error loading products and stats:', error);
                showMessage('加载数据失败: ' + error.message, false);
                showErrorState();
            }
        }

        // 仅加载产品数据
        async function loadProducts() {
            try {
                showLoadingState();

                const formData = getFilterParams();
                const params = new URLSearchParams(formData);

                params.append('page', currentPage);
                params.append('limit', pageSize);
                params.append('sortField', currentSortField);
                params.append('sortOrder', currentSortOrder);

                const response = await fetch(`/api/products?${params.toString()}`, {
                    headers: addApiKeyHeader()
                });

                if (response.status === 401) {
                    const data = await response.json();
                    if (data.error === "invalid_api_key") {
                        showApiKeyModal();
                        return;
                    }
                }

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '获取产品数据失败');
                }

                updateProductsTable(data.products || []);
                updatePagination(data.pagination || {});

            } catch (error) {
                console.error('Error loading products:', error);
                showMessage('加载产品数据失败: ' + error.message, false);
                showErrorState();
            }
        }

        // 获取筛选参数
        function getFilterParams() {
            const formData = new FormData(document.getElementById('filterForm'));
            const params = {};

            // 基本表单参数
            for (let [key, value] of formData.entries()) {
                if (value.trim() !== '') {
                    params[key] = value;
                }
            }

            // 添加多选框参数
            if (selectedProductSources.length > 0) {
                params.productSources = selectedProductSources.join(',');
            }

            if (selectedAllianceChannels.length > 0) {
                params.allianceChannels = selectedAllianceChannels.join(',');
            }

            if (selectedTags.length > 0) {
                params.tags = selectedTags.join(',');
            }

            if (selectedProductTypes.length > 0) {
                params.productTypes = selectedProductTypes.join(',');
            }

            return params;
        }

        // 更新产品表格
        function updateProductsTable(products) {
            const $tbody = $('#productsTable');

            if (!products || products.length === 0) {
                $tbody.html('<tr><td colspan="12" class="px-6 py-4 text-center text-sm text-gray-500">暂无数据</td></tr>');
                return;
            }

            let html = '';
            products.forEach(product => {
                html += `
                    <tr class="hover:bg-gray-50">
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">${product.product_id || '--'}</td>
                        <td class="px-3 py-4 text-sm text-gray-900 max-w-xs truncate" title="${product.product_title || '--'}">${product.product_title || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">¥${formatNumber(product.product_price) || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">${formatPercent(product.commission_rate) || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">¥${formatNumber(product.commission_amount) || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">${product.product_source || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">${product.alliance_channel || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">${formatNumber(product.sales_30_days) || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center ${getGrowthRateColor(product.sales_7_days_growth_rate)}">${formatPercent(product.sales_7_days_growth_rate) || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">${getTagName(product.tag) || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">${product.product_type || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">${product.date || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">${product.anchor_name || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center">${product.batch_number || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 text-center">${formatDate(product.created_at) || '--'}</td>
                    </tr>
                `;
            });

            $tbody.html(html);
        }

        // 更新统计卡片
        function updateStatsCards(stats) {
            $('#totalProducts').text(formatNumber(stats.totalProducts) || '--');
            $('#avgPrice').text(stats.avgPrice ? '¥' + formatNumber(stats.avgPrice) : '--');
            $('#avgCommission').text(stats.avgCommission ? '¥' + formatNumber(stats.avgCommission) : '--');
            $('#totalSales30').text(formatNumber(stats.totalSales30) || '--');
            $('#totalAnchors').text(formatNumber(stats.totalAnchors) || '--');
        }

        // 更新分页
        function updatePagination(pagination) {
            if (!pagination) return;

            currentPage = pagination.currentPage || 1;
            totalPages = pagination.totalPages || 1;
            const totalItems = pagination.totalItems || 0;

            // 更新总记录数
            $('#totalCount').text(totalItems);
            $('#totalItems').text(totalItems);

            // 更新移动端分页
            $('#currentPageMobile').text(currentPage);
            $('#totalPagesMobile').text(totalPages);

            // 更新桌面端分页信息
            const startItem = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;
            const endItem = Math.min(currentPage * pageSize, totalItems);
            $('#startItem').text(startItem);
            $('#endItem').text(endItem);

            // 更新分页按钮状态
            $('#prevPageMobile').prop('disabled', currentPage <= 1);
            $('#nextPageMobile').prop('disabled', currentPage >= totalPages);

            // 生成桌面端分页按钮
            generatePaginationButtons();
        }

        // 生成分页按钮
        function generatePaginationButtons() {
            const $pagination = $('#pagination');
            let html = '';

            // 上一页按钮
            html += `
                <button onclick="changePage(${currentPage - 1})"
                        ${currentPage <= 1 ? 'disabled' : ''}
                        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage <= 1 ? 'cursor-not-allowed opacity-50' : ''}">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;

            // 页码按钮
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                html += `
                    <button onclick="changePage(${i})"
                            class="relative inline-flex items-center px-4 py-2 border text-sm font-medium ${isActive
                                ? 'z-10 bg-orange-50 border-orange-500 text-orange-600'
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}">
                        ${i}
                    </button>
                `;
            }

            // 下一页按钮
            html += `
                <button onclick="changePage(${currentPage + 1})"
                        ${currentPage >= totalPages ? 'disabled' : ''}
                        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage >= totalPages ? 'cursor-not-allowed opacity-50' : ''}">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;

            $pagination.html(html);
        }

        // 切换页面
        function changePage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadProducts();
            }
        }

        // 排序功能
        function toggleSort(field) {
            if (currentSortField === field) {
                currentSortOrder = currentSortOrder === 'ASC' ? 'DESC' : 'ASC';
            } else {
                currentSortField = field;
                currentSortOrder = 'DESC';
            }

            // 更新排序图标
            updateSortIcons();

            // 重新加载数据
            currentPage = 1;
            loadProducts();
        }

        // 更新排序图标
        function updateSortIcons() {
            // 清除所有排序图标
            $('.sort-icon').removeClass('sort-asc sort-desc');

            // 设置当前排序字段的图标
            const $currentIcon = $(`#sort-${currentSortField}`);
            if (currentSortOrder === 'ASC') {
                $currentIcon.addClass('sort-asc');
            } else {
                $currentIcon.addClass('sort-desc');
            }
        }

        // 工具函数
        function formatNumber(num) {
            if (num === null || num === undefined || num === '') return '';
            return parseFloat(num).toLocaleString();
        }

        function formatPercent(num) {
            if (num === null || num === undefined || num === '') return '';
            return parseFloat(num).toFixed(2) + '%';
        }

        function formatDate(dateStr) {
            if (!dateStr) return "";
            try {
                const date = new Date(dateStr);
                return date.getFullYear() +
                      '-' + String(date.getMonth() + 1).padStart(2, '0') +
                      '-' + String(date.getDate()).padStart(2, '0') +
                      ' ' + String(date.getHours()).padStart(2, '0') +
                      ':' + String(date.getMinutes()).padStart(2, '0') +
                      ':' + String(date.getSeconds()).padStart(2, '0');
            } catch (e) {
                return dateStr;
            }
        }

        function getTagName(tag) {
            if (tag === null || tag === undefined || tag === '') return '';
            const tagMap = {
                1: '大类目',
                2: '流量品',
                3: '店铺品',
                4: '出单同类品'
            };
            return tagMap[parseInt(tag)] || '';
        }

        function getGrowthRateColor(rate) {
            if (rate === null || rate === undefined || rate === '') return 'text-gray-900';
            const numRate = parseFloat(rate);
            if (numRate > 0) return 'text-green-600';
            if (numRate < 0) return 'text-red-600';
            return 'text-gray-900';
        }

        // 显示加载状态
        function showLoadingState() {
            $('#productsTable').html('<tr><td colspan="12" class="px-6 py-4 text-center text-sm text-gray-500">加载中...</td></tr>');
        }

        // 显示错误状态
        function showErrorState() {
            $('#productsTable').html('<tr><td colspan="12" class="px-6 py-4 text-center text-sm text-red-500">加载失败，请重试</td></tr>');
        }

        // 显示消息
        function showMessage(message, isSuccess = true) {
            if (typeof layer !== 'undefined') {
                if (isSuccess) {
                    layer.msg(message, { icon: 1, time: 2000 });
                } else {
                    layer.msg(message, { icon: 2, time: 3000 });
                }
            } else {
                alert(message);
            }
        }

        // Cookie操作
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        function setCookie(name, value, days = 7) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
        }

        // 添加API密钥头
        function addApiKeyHeader() {
            const apiKey = getCookie('api_key');
            return apiKey ? { 'X-API-Key': apiKey } : {};
        }

        // 显示API密钥输入弹窗
        function showApiKeyModal() {
            $('#apiKeyModal').removeClass('hidden');
            $('#apiKey').focus();
        }

        // 隐藏API密钥输入弹窗
        function hideApiKeyModal() {
            $('#apiKeyModal').addClass('hidden');
            $('#apiKey').val('');
        }

        // 提交API密钥
        function submitApiKey() {
            const apiKey = $('#apiKey').val().trim();
            if (!apiKey) {
                showMessage('请输入访问密码', false);
                return;
            }

            // 保存到cookie
            setCookie('api_key', apiKey);
            hideApiKeyModal();

            // 重新加载数据
            Promise.all([
                loadAnchorNames(),
                loadProductsAndStats()
            ]).catch(error => {
                console.error('数据加载失败:', error);
                showMessage('数据加载失败，请检查访问密码', false);
            });
        }

        // 导出Excel功能
        async function exportToExcel() {
            try {
                if (typeof XLSX === 'undefined') {
                    showMessage('Excel导出功能未加载，请刷新页面重试', false);
                    return;
                }

                showMessage('正在导出数据，请稍候...', true);

                const formData = getFilterParams();
                const params = new URLSearchParams(formData);
                params.append('export', 'true');

                const response = await fetch(`/api/products/export?${params.toString()}`, {
                    headers: addApiKeyHeader()
                });

                if (response.status === 401) {
                    const data = await response.json();
                    if (data.error === "invalid_api_key") {
                        showApiKeyModal();
                        return;
                    }
                }

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '导出失败');
                }

                if (!data.products || data.products.length === 0) {
                    showMessage('没有数据可导出', false);
                    return;
                }

                // 准备导出数据
                const exportData = data.products.map(product => ({
                    '产品ID': product.product_id || '',
                    '产品标题': product.product_title || '',
                    '产品价格': product.product_price || '',
                    '佣金率(%)': product.commission_rate || '',
                    '佣金金额': product.commission_amount || '',
                    '产品来源': product.product_source || '',
                    '联盟商品渠道': product.alliance_channel || '',
                    '产品类目': product.product_category || '',
                    '365天销量': product.sales_365_days || '',
                    '30天销量': product.sales_30_days || '',
                    '7天销量': product.sales_7_days || '',
                    '7天销量增长率(%)': product.sales_7_days_growth_rate || '',
                    '30天订单数': product.orders_30_days || '',
                    '标签': getTagName(product.tag) || '',
                    '产品类型': product.product_type || '',
                    '日期': product.date || '',
                    '特征标签': product.feature_tags || '',
                    '批次号': product.batch_number || '',
                    '主播名称': product.anchor_name || '',
                    '创建时间': formatDate(product.created_at) || ''
                }));

                // 创建工作簿
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(exportData);

                // 设置列宽
                const colWidths = [
                    { wch: 15 }, // 产品ID
                    { wch: 30 }, // 产品标题
                    { wch: 12 }, // 产品价格
                    { wch: 10 }, // 佣金率
                    { wch: 12 }, // 佣金金额
                    { wch: 15 }, // 产品来源
                    { wch: 15 }, // 联盟商品渠道
                    { wch: 15 }, // 产品类目
                    { wch: 12 }, // 365天销量
                    { wch: 12 }, // 30天销量
                    { wch: 12 }, // 7天销量
                    { wch: 12 }, // 销量增长率
                    { wch: 12 }, // 30天订单数
                    { wch: 20 }, // 特征标签
                    { wch: 15 }, // 批次号
                    { wch: 15 }, // 主播名称
                    { wch: 20 }  // 创建时间
                ];
                ws['!cols'] = colWidths;

                XLSX.utils.book_append_sheet(wb, ws, "产品数据");

                // 生成文件名
                const now = new Date();
                const fileName = `产品数据_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}.xlsx`;

                // 下载文件
                XLSX.writeFile(wb, fileName);
                showMessage('导出成功！', true);

            } catch (error) {
                console.error('Export error:', error);
                showMessage('导出失败: ' + error.message, false);
            }
        }
    </script>
</body>
</html>
