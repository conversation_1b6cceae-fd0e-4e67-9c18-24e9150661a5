# 淘宝热销产品自动选品系统架构文档

## 项目概述

这是一个基于 Node.js + Express + SQLite 的淘宝热销产品自动选品管理系统，主要功能包括主播管理、产品数据采集、选品规则配置、数据统计分析等。

## 技术栈

### 后端技术
- **Node.js** - 运行时环境
- **Express.js** - Web 框架
- **Better-SQLite3** - 数据库驱动
- **Node-cron** - 定时任务调度
- **CORS** - 跨域资源共享
- **Dotenv** - 环境变量管理

### 前端技术
- **HTML5 + CSS3** - 页面结构和样式
- **TailwindCSS** - CSS 框架
- **jQuery** - JavaScript 库
- **Layer.js** - 弹窗组件
- **XLSX.js** - Excel 导入导出
- **Font Awesome** - 图标库

### 数据库
- **SQLite** - 轻量级关系型数据库

### 部署工具
- **PM2** - 进程管理器
- **Bash Scripts** - 自动化部署脚本

## 项目结构

```
taobao_hot_product_admin_v1/
├── src/                          # 源代码目录
│   ├── server.js                 # 主服务器入口文件
│   ├── index.js                  # API 路由定义
│   ├── config.js                 # 配置文件
│   ├── database.js               # 数据库连接和操作封装
│   ├── job.js                    # 定时任务和数据同步逻辑
│   ├── init-db.js               # 数据库初始化脚本
│   ├── run-job.js               # 手动执行任务脚本
│   └── condition.json           # 选品条件配置
├── public/                       # 静态资源目录
│   ├── products.html            # 产品管理页面
│   ├── anchors.html             # 主播管理页面
│   ├── selection-rules.html     # 选品规则配置页面
│   ├── rule-management.html     # 规则管理页面
│   └── js/                      # JavaScript 文件
│       └── selection-rules.js   # 选品规则前端逻辑
├── db/                          # 数据库目录
│   ├── hot-product-data.db      # 主数据库文件
│   └── init-database.sql       # 数据库初始化 SQL
├── node_modules/                # 依赖包目录
├── package.json                 # 项目配置文件
├── package-lock.json           # 依赖锁定文件
├── start.sh                    # PM2 管理脚本
└── deploy.sh                   # 自动化部署脚本
```

## 核心模块架构

### 1. 服务器层 (server.js)
- **职责**: 应用启动、中间件配置、路由注册、定时任务启动
- **功能**:
  - Express 应用初始化
  - CORS 和静态文件服务配置
  - 数据库初始化
  - 定时任务调度 (每小时第10分和40分执行)
  - 健康检查接口

### 2. 路由层 (index.js)
- **职责**: API 接口定义、请求处理、权限验证
- **主要接口**:
  - 主播管理: `/api/anchors/*`
  - 产品管理: `/api/products/*`
  - 选品规则: `/api/selection-rules/*`
  - 数据同步: `/sync-anchor`
  - Cookie 更新: `/api/update-cookie`

### 3. 数据访问层 (database.js)
- **职责**: 数据库连接管理、SQL 操作封装
- **特性**:
  - 连接池管理
  - 自动重连机制
  - 事务支持
  - 批量操作优化

### 4. 业务逻辑层 (job.js)
- **职责**: 核心业务逻辑实现
- **主要功能**:
  - 淘宝 API 数据采集
  - 多主播并行同步
  - 数据清洗和存储
  - 报表统计生成
  - 钉钉通知发送

### 5. 配置管理 (config.js)
- **职责**: 系统配置集中管理
- **配置项**:
  - 服务器配置 (端口、主机)
  - 数据库配置 (路径)
  - API 配置 (URL、令牌、分页)
  - 认证配置 (密码)
  - 定时任务配置 (Cron 表达式)
  - 钉钉配置 (机器人密钥)

## 数据库设计

### 核心表结构

#### 1. anchors (主播表)
```sql
CREATE TABLE anchors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    anchor_name TEXT NOT NULL,           -- 主播名称
    anchor_id TEXT UNIQUE NOT NULL,      -- 主播ID
    anchor_cookie TEXT,                  -- 主播Cookie
    password TEXT,                       -- 访问密码
    status TEXT DEFAULT 'active',        -- 状态
    total_orders INTEGER DEFAULT 0,      -- 总订单数
    total_amount REAL DEFAULT 0,         -- 总金额
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. products (产品表)
```sql
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    anchor_name TEXT NOT NULL,           -- 主播名称
    product_id TEXT NOT NULL,            -- 产品ID
    product_title TEXT,                  -- 产品标题
    product_price DECIMAL(10,2) NOT NULL, -- 产品价格
    commission_rate DECIMAL(5,2),        -- 佣金率
    commission_amount DECIMAL(10,2),     -- 佣金金额
    product_source TEXT,                 -- 产品来源
    alliance_channel TEXT,               -- 联盟渠道
    sales_365_days INTEGER DEFAULT 0,    -- 365天销量
    sales_30_days INTEGER DEFAULT 0,     -- 30天销量
    sales_7_days INTEGER DEFAULT 0,      -- 7天销量
    sales_7_days_growth_rate DECIMAL(5,2), -- 7天增长率
    shop_name TEXT,                      -- 店铺名称
    batch_number TEXT,                   -- 批次号
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. selection_rules (选品规则表)
```sql
CREATE TABLE selection_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,             -- 规则名称
    description TEXT,                    -- 规则描述
    total_target INTEGER DEFAULT 500,    -- 总目标数量
    rule_groups TEXT NOT NULL,           -- 规则组配置(JSON)
    comprehensive_config TEXT,           -- 综合配置(JSON)
    status TEXT DEFAULT 'active',        -- 状态
    created_by TEXT,                     -- 创建者
    last_used_at DATETIME,              -- 最后使用时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. selection_executions (选品执行记录表)
```sql
CREATE TABLE selection_executions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_id INTEGER NOT NULL,            -- 关联规则ID
    rule_name TEXT NOT NULL,             -- 规则名称快照
    execution_time DATETIME NOT NULL,    -- 执行时间
    total_selected INTEGER DEFAULT 0,    -- 实际选中数量
    execution_result TEXT,               -- 执行结果详情(JSON)
    batch_number TEXT,                   -- 生成的批次号
    status TEXT DEFAULT 'completed',     -- 执行状态
    error_message TEXT,                  -- 错误信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 核心功能模块

### 1. 主播管理模块
- **功能**: 主播信息的增删改查
- **特性**:
  - 主播 Cookie 管理
  - 密码验证机制
  - 状态管理 (active/invalid)
  - 权限控制 (管理员/主播)

### 2. 产品数据采集模块
- **功能**: 从淘宝 API 采集产品数据
- **特性**:
  - 多主播并行采集
  - 智能时间范围计算
  - 数据去重和清洗
  - 批量数据存储
  - 错误处理和重试机制

### 3. 选品规则引擎
- **功能**: 基于规则的自动选品
- **特性**:
  - 多维度筛选条件
  - 规则组合逻辑
  - 预览功能
  - 执行历史记录
  - 批次管理

### 4. 数据统计分析
- **功能**: 产品和主播数据统计
- **特性**:
  - 实时统计计算
  - 多维度数据分析
  - 报表生成
  - 数据导出功能

### 5. 通知系统
- **功能**: 钉钉机器人通知
- **特性**:
  - 佣金统计通知
  - Cookie 失效警告
  - 同步状态通知

## 权限控制系统

### 用户类型
1. **管理员用户** - 拥有所有权限
2. **主播用户** - 只能查看自己的数据

### 权限验证流程
1. API Key 验证 (Header 或 Cookie)
2. 用户类型识别
3. 数据访问权限过滤
4. 操作权限检查

## 定时任务系统

### 任务调度
- **调度器**: Node-cron
- **执行时间**: 每小时第10分和40分 (6:00-23:00)
- **时区**: Asia/Shanghai

### 任务流程
1. 获取活跃主播列表
2. 并行数据采集 (批次处理)
3. 数据清洗和存储
4. 报表统计生成
5. 钉钉通知发送

## 部署架构

### 生产环境部署
- **进程管理**: PM2
- **服务器**: Linux (Debian/Ubuntu)
- **用户**: www-data
- **端口**: 3000
- **日志**: 集中日志管理

### 自动化部署
- **部署脚本**: deploy.sh
- **功能**: 一键部署、更新、回滚
- **监控**: 服务状态检查、连接测试

## 安全特性

### 数据安全
- SQLite 数据库文件权限控制
- SQL 注入防护 (参数化查询)
- 敏感信息加密存储

### 访问安全
- API Key 认证
- Cookie 有效性验证
- 权限分级控制
- CORS 跨域保护

### 运行安全
- 进程隔离 (非 root 用户运行)
- 错误处理和日志记录
- 资源限制和监控

## 性能优化

### 数据库优化
- 索引优化
- 批量操作
- 连接池管理
- 查询优化

### 应用优化
- 异步处理
- 并发控制
- 内存管理
- 缓存策略

## 监控和日志

### 日志系统
- 应用日志 (PM2)
- 错误日志
- 访问日志
- 同步日志

### 监控指标
- 服务状态
- 数据库连接
- 内存使用
- 同步成功率

## 扩展性设计

### 水平扩展
- 多实例部署支持
- 负载均衡兼容
- 数据库分离

### 功能扩展
- 插件化架构
- 配置化规则引擎
- API 接口标准化
- 微服务拆分准备

## 维护和运维

### 日常维护
- 数据备份策略
- 日志清理机制
- 性能监控
- 安全更新

### 故障处理
- 错误恢复机制
- 数据一致性检查
- 服务重启策略
- 问题诊断工具

这个架构文档详细描述了淘宝热销产品自动选品系统的技术架构、核心模块、数据设计和部署方案，为系统的开发、维护和扩展提供了全面的技术指导。
